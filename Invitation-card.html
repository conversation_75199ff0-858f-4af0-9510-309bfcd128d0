<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دعوة افتتاح مركز العمل المقدس الصحي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Amiri:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-blue: #1e40af;
            --secondary-blue: #3b82f6;
            --accent-gold: #f59e0b;
            --light-blue: #eff6ff;
            --medical-teal: #0891b2;
            --soft-gray: #f1f5f9;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', '<PERSON><PERSON>', sans-serif;
            background: #f0f4f8;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 15px;
        }

        .invitation-container {
            width: 100%;
            max-width: 1200px;
            aspect-ratio: 16/9;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .invitation-card {
            background: linear-gradient(145deg, #ffffff 0%, #fafbfc 50%, #f8fafc 100%);
            border-radius: 20px;
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.8),
                inset 0 1px 0 rgba(255, 255, 255, 1);
            width: 100%;
            height: 100%;
            padding: 30px;
            display: grid;
            grid-template-rows: auto 1fr auto;
            gap: 20px;
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(30, 64, 175, 0.1);
        }

        /* Health-related background illustrations */
        .invitation-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-blue), var(--accent-gold), var(--medical-teal));
            border-radius: 20px 20px 0 0;
            z-index: 1;
        }

        .invitation-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 400"><g opacity="0.03"><path d="M200 50 L210 90 L250 90 L220 110 L230 150 L200 130 L170 150 L180 110 L150 90 L190 90 Z" fill="%23059669"/><circle cx="100" cy="100" r="30" fill="none" stroke="%23059669" stroke-width="2"/><path d="M100 85 L100 115 M85 100 L115 100" stroke="%23059669" stroke-width="3"/><circle cx="300" cy="300" r="25" fill="none" stroke="%230891b2" stroke-width="2"/><path d="M300 285 L300 315 M285 300 L315 300" stroke="%230891b2" stroke-width="3"/><path d="M50 250 Q75 225 100 250 T150 250" fill="none" stroke="%231e40af" stroke-width="2"/><circle cx="350" cy="100" r="3" fill="%23f59e0b"/><circle cx="50" cy="350" r="3" fill="%23f59e0b"/><circle cx="350" cy="350" r="3" fill="%23f59e0b"/></g></svg>'),
                radial-gradient(circle at 20% 20%, rgba(30, 64, 175, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(8, 145, 178, 0.05) 0%, transparent 50%);
            background-size: 400px 400px, 300px 300px, 300px 300px;
            background-position: center, top left, bottom right;
            background-repeat: no-repeat;
            pointer-events: none;
            z-index: 0;
        }

        /* Medical icons background pattern */
        .medical-pattern {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><g opacity="0.02"><circle cx="20" cy="20" r="8" fill="none" stroke="%23059669" stroke-width="1"/><path d="M20 15 L20 25 M15 20 L25 20" stroke="%23059669" stroke-width="1.5"/><circle cx="80" cy="80" r="6" fill="none" stroke="%230891b2" stroke-width="1"/><path d="M80 76 L80 84 M76 80 L84 80" stroke="%230891b2" stroke-width="1.5"/><path d="M50 10 L52 18 L60 18 L54 22 L56 30 L50 26 L44 30 L46 22 L40 18 L48 18 Z" fill="%231e40af"/></g></svg>');
            background-size: 100px 100px;
            background-repeat: repeat;
            pointer-events: none;
            z-index: 0;
        }

        .invitation-header {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            position: relative;
            z-index: 3;
            padding-bottom: 10px;
        }

        .logo-container {
            position: relative;
            margin-bottom: 15px;
            animation: logoFloat 3s ease-in-out infinite;
        }

        .invitation-logo {
            width: 80px;
            height: auto;
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
            transition: transform 0.3s ease;
        }

        .invitation-logo:hover {
            transform: scale(1.05);
        }

        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-3px); }
        }

        .organization-title {
            font-family: 'Amiri', serif;
            font-size: clamp(1.2rem, 3vw, 1.8rem);
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-blue), var(--accent-gold));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 5px;
            line-height: 1.2;
        }

        .organization-subtitle {
            font-size: clamp(0.8rem, 2vw, 1rem);
            color: #64748b;
            font-weight: 500;
            margin-bottom: 0;
        }

        .main-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            flex: 1;
            position: relative;
            z-index: 3;
            padding: 10px 0;
        }

        .invitation-title {
            font-family: 'Amiri', serif;
            font-size: clamp(1.4rem, 4vw, 2.2rem);
            font-weight: 700;
            margin-bottom: 15px;
            line-height: 1.3;
        }

        .special-invitation {
            color: var(--primary-blue);
            position: relative;
        }

        .special-invitation::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--accent-gold), transparent);
            border-radius: 1px;
        }

        .health-center {
            color: var(--medical-teal);
            text-shadow: 0 2px 4px rgba(8, 145, 178, 0.1);
        }

        .invitation-description {
            font-size: clamp(0.9rem, 2.5vw, 1.1rem);
            color: #374151;
            line-height: 1.6;
            margin-bottom: 20px;
            max-width: 90%;
            font-weight: 400;
        }

        .event-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            width: 100%;
            max-width: 100%;
            margin-bottom: 0;
        }

        .detail-card {
            background: linear-gradient(145deg, #f8fafc, #ffffff);
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(30, 64, 175, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            text-align: center;
        }

        .detail-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--secondary-blue), var(--accent-gold));
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .detail-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
        }

        .detail-card:hover::before {
            transform: scaleX(1);
        }

        .detail-icon {
            font-size: 1.2rem;
            color: var(--secondary-blue);
            margin-bottom: 8px;
            display: block;
        }

        .detail-text {
            font-size: clamp(0.8rem, 2vw, 0.95rem);
            color: #374151;
            font-weight: 500;
            line-height: 1.3;
        }

        .location-card {
            grid-column: 1 / -1;
        }

        .contact-section {
            background: linear-gradient(145deg, var(--light-blue), #ffffff);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(30, 64, 175, 0.15);
            position: relative;
            z-index: 3;
        }

        .contact-title {
            font-size: clamp(0.9rem, 2.5vw, 1rem);
            color: #374151;
            margin-bottom: 12px;
            font-weight: 600;
            text-align: center;
        }

        .contact-links {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px;
        }

        .contact-link {
            display: flex;
            align-items: center;
            gap: 6px;
            color: var(--secondary-blue);
            text-decoration: none;
            font-size: clamp(0.8rem, 2vw, 0.9rem);
            font-weight: 500;
            padding: 6px 12px;
            border-radius: 10px;
            background: rgba(59, 130, 246, 0.1);
            transition: all 0.3s ease;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .contact-link:hover {
            background: var(--secondary-blue);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(59, 130, 246, 0.3);
        }

        .contact-link i {
            font-size: 1em;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .invitation-container {
                aspect-ratio: 9/16;
                max-width: 450px;
            }

            .invitation-card {
                padding: 20px;
                gap: 15px;
            }

            .event-details {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .contact-links {
                flex-direction: column;
                align-items: center;
                gap: 8px;
            }

            .contact-link {
                width: 100%;
                max-width: 200px;
                justify-content: center;
            }
        }

        @media (max-width: 480px) {
            .invitation-card {
                padding: 15px;
                gap: 12px;
            }

            .invitation-description {
                max-width: 95%;
            }

            .detail-card {
                padding: 12px;
            }

            .contact-section {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="invitation-container">
        <div class="invitation-card">
            <!-- Header Section -->
            <div class="invitation-header">
                <div class="logo-container">
                    <img src="https://holywork.net/img/logo-original.svg" alt="Holy Work Organization Logo" class="invitation-logo">
                </div>
                <h1 class="organization-title">منظمة العمل المقدس للتنمية الدولية</h1>
                <p class="organization-subtitle">International Holy Work Organization for Development</p>
            </div>

            <!-- Main Content Section -->
            <div class="main-content">
                <h2 class="invitation-title">
                    <span class="special-invitation">دعوة خاصة</span> لحضور حفل افتتاح
                    <br>
                    <span class="health-center">مركز العمل المقدس الصحي الجديد</span>
                </h2>

                <p class="invitation-description">
                    يشرفنا ويسعدنا دعوتكم لحضور حفل افتتاح مركزنا الصحي الجديد، والذي يهدف إلى تقديم أفضل الخدمات الطبية والرعاية الصحية لمجتمعنا. حضوركم الكريم يضيف بهجة وسروراً إلى هذا الحدث الهام.
                </p>

                <!-- Event Details -->
                <div class="event-details">
                    <div class="detail-card">
                        <i class="fas fa-calendar-alt detail-icon"></i>
                        <div class="detail-text">التاريخ: 26 يونيو 2025</div>
                    </div>
                    <div class="detail-card">
                        <i class="fas fa-clock detail-icon"></i>
                        <div class="detail-text">الوقت: 11:00 صباحاً</div>
                    </div>
                    <div class="detail-card location-card">
                        <i class="fas fa-map-marker-alt detail-icon"></i>
                        <div class="detail-text">الموقع: مركز العمل المقدس الصحي، مخيم دوميز 1، دهوك</div>
                    </div>
                </div>
            </div>

            <!-- Contact Section -->
            <div class="contact-section">
                <div class="contact-title">للاستفسار ولمزيد من المعلومات:</div>
                <div class="contact-links">
                    <a href="tel:+9647510136642" class="contact-link">
                        <i class="fas fa-phone-alt"></i>
                        <span>0751 013 6642</span>
                    </a>
                    <a href="mailto:<EMAIL>" class="contact-link">
                        <i class="fas fa-envelope"></i>
                        <span><EMAIL></span>
                    </a>
                    <a href="https://www.holywork.net" target="_blank" class="contact-link">
                        <i class="fas fa-globe"></i>
                        <span>www.holywork.net</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
