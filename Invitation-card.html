<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دعوة افتتاح مركز العمل المقدس الصحي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Amiri:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-blue: #1e40af;
            --secondary-blue: #3b82f6;
            --accent-gold: #f59e0b;
            --light-blue: #eff6ff;
            --gradient-start: #1e3a8a;
            --gradient-end: #3b82f6;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', '<PERSON><PERSON>', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-attachment: fixed;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
            z-index: 1;
        }

        .invitation-container {
            position: relative;
            z-index: 2;
            width: 100%;
            max-width: 1200px;
            aspect-ratio: 16/9;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .invitation-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 24px;
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
            width: 100%;
            height: 100%;
            padding: 40px;
            display: grid;
            grid-template-rows: auto 1fr auto;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .invitation-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-blue), var(--accent-gold), var(--secondary-blue));
            border-radius: 24px 24px 0 0;
        }

        .invitation-card::after {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
            pointer-events: none;
        }

        .invitation-header {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            position: relative;
            z-index: 3;
        }

        .logo-container {
            position: relative;
            margin-bottom: 20px;
            animation: logoFloat 3s ease-in-out infinite;
        }

        .invitation-logo {
            width: 100px;
            height: auto;
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
            transition: transform 0.3s ease;
        }

        .invitation-logo:hover {
            transform: scale(1.05);
        }

        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
        }

        .organization-title {
            font-family: 'Amiri', serif;
            font-size: clamp(1.5rem, 4vw, 2.5rem);
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-blue), var(--accent-gold));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .organization-subtitle {
            font-size: clamp(0.9rem, 2.5vw, 1.2rem);
            color: #64748b;
            font-weight: 500;
            margin-bottom: 20px;
        }

        .main-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            flex: 1;
            position: relative;
            z-index: 3;
        }

        .invitation-title {
            font-family: 'Amiri', serif;
            font-size: clamp(1.8rem, 5vw, 3rem);
            font-weight: 700;
            margin-bottom: 20px;
            line-height: 1.3;
        }

        .special-invitation {
            color: var(--primary-blue);
            position: relative;
        }

        .special-invitation::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--accent-gold), transparent);
            border-radius: 2px;
        }

        .health-center {
            color: #059669;
            text-shadow: 0 2px 4px rgba(5, 150, 105, 0.1);
        }

        .invitation-description {
            font-size: clamp(1rem, 3vw, 1.3rem);
            color: #374151;
            line-height: 1.8;
            margin-bottom: 30px;
            max-width: 80%;
            font-weight: 400;
        }

        .event-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            width: 100%;
            max-width: 800px;
            margin-bottom: 30px;
        }

        .detail-card {
            background: linear-gradient(145deg, #f8fafc, #ffffff);
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(59, 130, 246, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .detail-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--secondary-blue), var(--accent-gold));
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .detail-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .detail-card:hover::before {
            transform: scaleX(1);
        }

        .detail-icon {
            font-size: 1.5rem;
            color: var(--secondary-blue);
            margin-bottom: 10px;
            display: block;
        }

        .detail-text {
            font-size: clamp(0.9rem, 2.5vw, 1.1rem);
            color: #374151;
            font-weight: 500;
            line-height: 1.4;
        }

        .location-card {
            grid-column: 1 / -1;
        }

        .contact-section {
            background: linear-gradient(145deg, var(--light-blue), #ffffff);
            border-radius: 20px;
            padding: 25px;
            border: 1px solid rgba(59, 130, 246, 0.2);
            position: relative;
            z-index: 3;
        }

        .contact-title {
            font-size: clamp(1rem, 3vw, 1.2rem);
            color: #374151;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .contact-links {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 15px;
        }

        .contact-link {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--secondary-blue);
            text-decoration: none;
            font-size: clamp(0.9rem, 2.5vw, 1rem);
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 12px;
            background: rgba(59, 130, 246, 0.1);
            transition: all 0.3s ease;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .contact-link:hover {
            background: var(--secondary-blue);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .contact-link i {
            font-size: 1.1em;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .invitation-container {
                aspect-ratio: 9/16;
                max-width: 400px;
            }

            .invitation-card {
                padding: 25px;
            }

            .event-details {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .contact-links {
                flex-direction: column;
                align-items: center;
            }
        }

        @media (max-width: 480px) {
            .invitation-card {
                padding: 20px;
            }

            .invitation-description {
                max-width: 95%;
            }
        }
    </style>
</head>
<body>
    <div class="invitation-container">
        <div class="invitation-card">
            <!-- Header Section -->
            <div class="invitation-header">
                <div class="logo-container">
                    <img src="https://holywork.net/img/logo-original.svg" alt="Holy Work Organization Logo" class="invitation-logo">
                </div>
                <h1 class="organization-title">منظمة العمل المقدس للتنمية الدولية</h1>
                <p class="organization-subtitle">International Holy Work Organization for Development</p>
            </div>

            <!-- Main Content Section -->
            <div class="main-content">
                <h2 class="invitation-title">
                    <span class="special-invitation">دعوة خاصة</span> لحضور حفل افتتاح
                    <br>
                    <span class="health-center">مركز العمل المقدس الصحي الجديد</span>
                </h2>

                <p class="invitation-description">
                    يشرفنا ويسعدنا دعوتكم لحضور حفل افتتاح مركزنا الصحي الجديد، والذي يهدف إلى تقديم أفضل الخدمات الطبية والرعاية الصحية لمجتمعنا. حضوركم الكريم يضيف بهجة وسروراً إلى هذا الحدث الهام.
                </p>

                <!-- Event Details -->
                <div class="event-details">
                    <div class="detail-card">
                        <i class="fas fa-calendar-alt detail-icon"></i>
                        <div class="detail-text">التاريخ: 26 يونيو 2025</div>
                    </div>
                    <div class="detail-card">
                        <i class="fas fa-clock detail-icon"></i>
                        <div class="detail-text">الوقت: 11:00 صباحاً</div>
                    </div>
                    <div class="detail-card location-card">
                        <i class="fas fa-map-marker-alt detail-icon"></i>
                        <div class="detail-text">الموقع: مركز العمل المقدس الصحي، مخيم دوميز 1، دهوك</div>
                    </div>
                </div>
            </div>

            <!-- Contact Section -->
            <div class="contact-section">
                <div class="contact-title">للاستفسار ولمزيد من المعلومات:</div>
                <div class="contact-links">
                    <a href="tel:+9647510136642" class="contact-link">
                        <i class="fas fa-phone-alt"></i>
                        <span>0751 013 6642</span>
                    </a>
                    <a href="mailto:<EMAIL>" class="contact-link">
                        <i class="fas fa-envelope"></i>
                        <span><EMAIL></span>
                    </a>
                    <a href="https://www.holywork.net" target="_blank" class="contact-link">
                        <i class="fas fa-globe"></i>
                        <span>www.holywork.net</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
